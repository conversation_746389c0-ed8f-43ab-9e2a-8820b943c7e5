# Lia AI Assistant - Environment Variables Template
# Copy this file to .env and fill in your actual API keys

# Required for web search functionality
GOOGLE_SEARCH_API_KEY=your_google_search_api_key_here
SEARCH_ENGINE_ID=your_custom_search_engine_id_here

# Optional: For advanced vision analysis using Google Gemini
GEMINI_API_KEY=your_gemini_api_key_here

# Optional: For future LinkedIn integration features
LINKEDIN_ACCESS_TOKEN=your_linkedin_access_token_here

# Optional: For GitHub repository search and integration
GITHUB_TOKEN=your_github_personal_access_token_here

# Instructions:
# 1. Copy this file: cp .env.example .env
# 2. Get Google Search API key from: https://developers.google.com/custom-search/v1/introduction
# 3. Create Custom Search Engine at: https://cse.google.com/cse/
# 4. Get Gemini API key from: https://makersuite.google.com/app/apikey
# 5. Get LinkedIn token from: https://developer.linkedin.com/
# 6. Get GitHub token from: https://github.com/settings/tokens

# Note: Only GOOGLE_SEARCH_API_KEY and SEARCH_ENGINE_ID are required for basic functionality
# Other API keys are optional and enable additional features