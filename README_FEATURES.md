# Lia AI Assistant - Enhanced Features Documentation

## Overview
Lia is an advanced AI assistant built with LiveKit and Google's realtime model, enhanced with powerful computer vision, web search, and code debugging capabilities. All commands work in Roman Urdu for natural Pakistani/Indian interaction.

## 🚀 Core Features (Implemented)

### 1. Screenshot Functionality
**Description**: Capture and manage screenshots with automatic timestamp naming.

**Roman Urdu Commands**:
- `screenshot lo` - Take full screen screenshot
- `screen capture karo` - Take full screen screenshot
- `tasveer lo` - Take full screen screenshot
- `region screenshot lo` - Take screenshot of specific area
- `screenshots dikhao` - List all saved screenshots

**Features**:
- Automatic timestamp naming (screenshot_YYYY-MM-DD_HH-MM-SS.png)
- Screenshots saved to `screenshots/` folder
- Region-specific screenshots with coordinates
- File size and count information

**Example Usage**:
```
User: "Screenshot lo"
Lia: "Screenshot successfully save ho gaya: screenshots/screenshot_2024-01-15_14-30-25.png"
```

### 2. Screen Analysis with Vision API
**Description**: Advanced screen analysis using computer vision and OCR.

**Roman Urdu Commands**:
- `screen analyze karo` - Basic screen analysis
- `screen dekh kar batao` - Analyze current screen
- `screen mein [text] dhundo` - Find specific text on screen
- `screen ko detail mein dekho` - Advanced Gemini Vision analysis
- `objects detect karo` - Detect objects on screen

**Features**:
- OCR text extraction using Tesseract
- Object detection using OpenCV
- Color analysis and brightness detection
- Google Gemini Vision API integration
- Shape detection (rectangles, circles, triangles)

**Example Usage**:
```
User: "screen analyze karo"
Lia: "Screen Analysis:
Text Content: Welcome to Visual Studio Code...
Visual Analysis: Screen size: 1920x1080, Brightness: 128.5/255, Objects detected: 45, Screen brightness normal hai."
```

### 3. Enhanced Web Browser Integration
**Description**: Intelligent web search for code solutions, tutorials, and tech news.

**Roman Urdu Commands**:
- `error solve karo` - Search for code error solutions
- `code fix dhundo` - Find code fixes
- `tutorial dhundo` - Search programming tutorials
- `tech news dikhao` - Get latest tech news
- `website kholo [URL]` - Open website in browser
- `docs dhundo [technology]` - Search documentation
- `github repo dhundo [topic]` - Find GitHub repositories

**Features**:
- Prioritized results from StackOverflow, GitHub, official docs
- Automatic browser opening for documentation
- Code-specific search optimization
- Educational resource prioritization

**Example Usage**:
```
User: "python error solve karo: ModuleNotFoundError"
Lia: "Code Solutions for 'ModuleNotFoundError':
⭐ 1. How to fix ModuleNotFoundError in Python - Stack Overflow
🔗 https://stackoverflow.com/questions/...
📝 This error occurs when Python cannot find the module..."
```

### 4. Code Debugging Assistant
**Description**: Comprehensive code analysis and debugging assistance.

**Roman Urdu Commands**:
- `code check karo [file_path]` - Analyze Python file for issues
- `file mein errors dhundo` - Find errors in code file
- `code run karo [file_path]` - Execute Python code safely
- `project check karo [folder]` - Analyze entire project
- `error fix karo [error_message]` - Get fix suggestions

**Features**:
- Syntax error detection
- Code quality analysis
- Security vulnerability scanning
- Import statement analysis
- Runtime error capture with 30-second timeout
- Project-wide analysis

**Example Usage**:
```
User: "code check karo agent.py"
Lia: "Code Analysis Results for 'agent.py':
✅ File 'agent.py' mein koi major issues nahi mile. Code looks good!"
```

## 🔧 Installation & Setup

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Required API Keys
Create a `.env` file in the project root:
```env
# Required for web search
GOOGLE_SEARCH_API_KEY=your_google_search_api_key
SEARCH_ENGINE_ID=your_custom_search_engine_id

# Optional for advanced vision analysis
GEMINI_API_KEY=your_gemini_api_key

# Optional for future LinkedIn integration
LINKEDIN_ACCESS_TOKEN=your_linkedin_token

# Optional for GitHub integration
GITHUB_TOKEN=your_github_token
```

### 3. Additional Setup for OCR
**Windows**: Download and install Tesseract OCR from https://github.com/UB-Mannheim/tesseract/wiki
**Linux**: `sudo apt-get install tesseract-ocr`
**macOS**: `brew install tesseract`

## 📁 Project Structure
```
Lia/
├── agent.py                 # Main agent file
├── config/
│   ├── prompts.py          # AI behavior prompts
│   └── settings.py         # Configuration settings
├── tools/
│   ├── screenshot_tool.py   # Screenshot functionality
│   ├── screen_analysis.py   # Screen analysis & vision
│   ├── enhanced_web_search.py # Web search tools
│   ├── code_debugger.py     # Code debugging tools
│   └── [existing tools...]
├── screenshots/            # Auto-created for screenshots
├── notes/                  # Auto-created for notes
├── requirements.txt        # Python dependencies
└── README_FEATURES.md      # This documentation
```

## 🎯 Usage Examples

### Screenshot Management
```
# Take screenshot
"screenshot lo"

# Take region screenshot
"region screenshot lo" (with coordinates: x=100, y=100, width=500, height=300)

# List screenshots
"screenshots dikhao"
```

### Screen Analysis
```
# Basic analysis
"screen analyze karo"

# Find specific text
"screen mein 'Python' dhundo"

# Advanced AI analysis
"screen ko detail mein dekho"

# Object detection
"objects detect karo"
```

### Web Search & Documentation
```
# Search for error solutions
"python ImportError solve karo"

# Find tutorials
"React tutorial dhundo beginner"

# Get tech news
"AI tech news dikhao"

# Open documentation
"Python docs dhundo"

# Find GitHub repos
"machine learning github repo dhundo"
```

### Code Debugging
```
# Analyze single file
"code check karo tools/screenshot_tool.py"

# Run code safely
"code run karo test_script.py"

# Analyze entire project
"project check karo ."

# Get error fix suggestions
"NameError fix karo: name 'variable' is not defined"
```

## ⚙️ Configuration

### Feature Flags (config/settings.py)
```python
ENABLE_GEMINI_VISION = True      # Advanced AI vision analysis
ENABLE_ADVANCED_OCR = True       # Enhanced OCR capabilities
ENABLE_CODE_EXECUTION = True     # Safe code execution
ENABLE_WEB_AUTOMATION = True     # Browser automation
ENABLE_LINKEDIN_INTEGRATION = False  # LinkedIn API (requires setup)
ENABLE_GITHUB_INTEGRATION = True     # GitHub search
```

### Customizable Settings
- Screenshot folder and format
- OCR language settings
- Search result limits
- Code analysis parameters
- API timeouts and rate limits

## 🔒 Security Features

### Code Analysis Security Checks
- `eval()` and `exec()` usage detection
- Hardcoded credential scanning
- SQL injection pattern detection
- Subprocess shell injection warnings

### Safe Code Execution
- 30-second timeout limit
- Isolated subprocess execution
- Error capture and reporting
- No direct system access

## 🚧 Advanced Features (Priority 2 - To Be Implemented)

### 5. Vision-based Object Recognition
- Advanced computer vision for object identification
- "What's in my hand" type queries
- Real-time object tracking

### 6. Note-taking System
- Structured note storage with timestamps
- Markdown format support
- Search and retrieval capabilities

### 7. Reminder System
- Scheduled notifications
- Recurring reminders
- Sound alerts

### 8. LinkedIn API Integration
- Post updates
- Send messages
- Network management

### 9. Git Repository Search
- Advanced GitHub repository search
- Code pattern matching
- Repository analysis

## 🐛 Troubleshooting

### Common Issues

1. **OCR not working**: Install Tesseract OCR and ensure it's in PATH
2. **Gemini Vision errors**: Check GEMINI_API_KEY in .env file
3. **Web search failing**: Verify GOOGLE_SEARCH_API_KEY and SEARCH_ENGINE_ID
4. **Screenshot permission**: Grant screen capture permissions on macOS
5. **Import errors**: Run `pip install -r requirements.txt`

### Error Messages
- "API key nahi mila": Add required API keys to .env file
- "File nahi mila": Check file path spelling and existence
- "Screen capture karne mein error": Check screen capture permissions

## 📞 Support

For issues or feature requests:
1. Check the troubleshooting section
2. Verify API key configuration
3. Ensure all dependencies are installed
4. Check file permissions and paths

## 🔄 Updates

This documentation covers the core features implemented. Advanced features (Priority 2) will be added in future updates based on user feedback and requirements.

---
**Built by**: Usama Adeel
**AI Model**: Claude Sonnet 4 by Anthropic via LiveKit
**Language**: Roman Urdu for natural Pakistani/Indian interaction