from dotenv import load_dotenv

from livekit import agents
from livekit.agents import Agent<PERSON><PERSON><PERSON>, Agent, RoomInputOptions
from livekit.plugins import (
    google,
    noise_cancellation,
)
from config.prompts import behavior_prompts, Reply_prompts
from tools.google_search import google_search, get_current_datetime
from tools.get_whether import get_weather
from tools.window_CTRL import open, close, folder_file
from tools.file_opner import Play_file
from tools.system_info import get_system_info, get_battery_info
from tools.system_control import move_cursor_tool, mouse_click_tool, scroll_cursor_tool, type_text_tool, press_key_tool, swipe_gesture_tool, press_hotkey_tool, control_volume_tool
from tools.screenshot_tool import take_screenshot, take_region_screenshot, list_screenshots
from tools.screen_analysis import analyze_current_screen, find_on_screen, analyze_screen_with_gemini, detect_objects_on_screen
from tools.enhanced_web_search import search_code_solutions, search_programming_tutorials, search_tech_news, open_website, search_and_open, search_documentation, search_github_repos
from tools.code_debugger import analyze_python_file, run_python_code, analyze_project_folder, suggest_error_fix
load_dotenv()


class Assistant(Agent):
    def __init__(self) -> None:
        super().__init__(instructions=behavior_prompts,
                         tools=[
                            google_search,
                            get_current_datetime,
                            get_weather,
                            open,
                            close, 
                            folder_file,
                            Play_file, 
                            move_cursor_tool,
                            mouse_click_tool,
                            scroll_cursor_tool,
                            type_text_tool,
                            press_key_tool,
                            press_hotkey_tool,
                            control_volume_tool,
                            swipe_gesture_tool,
                            get_system_info,
                            get_battery_info,
                            # Screenshot tools
                            take_screenshot,
                            take_region_screenshot,
                            list_screenshots,
                            # Screen analysis tools
                            analyze_current_screen,
                            find_on_screen,
                            analyze_screen_with_gemini,
                            detect_objects_on_screen,
                            # Enhanced web search tools
                            search_code_solutions,
                            search_programming_tutorials,
                            search_tech_news,
                            open_website,
                            search_and_open,
                            search_documentation,
                            search_github_repos,
                            # Code debugging tools
                            analyze_python_file,
                            run_python_code,
                            analyze_project_folder,
                            suggest_error_fix,
                         ]
                         )


async def entrypoint(ctx: agents.JobContext):
    session = AgentSession(
        llm=google.beta.realtime.RealtimeModel(
            voice="Charon"
        )
    )
    
    await session.start(
        room=ctx.room,
        agent=Assistant(),
        room_input_options=RoomInputOptions(
            noise_cancellation=noise_cancellation.BVC(),
            video_enabled=True 
        ),
    )

    await ctx.connect()

    await session.generate_reply(
        instructions=Reply_prompts
    )


if __name__ == "__main__":
    agents.cli.run_app(agents.WorkerOptions(entrypoint_fnc=entrypoint))

