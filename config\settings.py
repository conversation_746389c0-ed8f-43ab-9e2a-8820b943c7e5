"""
Configuration settings for Lia AI Assistant
"""
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# API Keys and Credentials
GOOGLE_SEARCH_API_KEY = os.getenv('GOOGLE_SEARCH_API_KEY')
SEARCH_ENGINE_ID = os.getenv('SEARCH_ENGINE_ID')
GEMINI_API_KEY = os.getenv('GEMINI_API_KEY')
LINKEDIN_ACCESS_TOKEN = os.getenv('LINKEDIN_ACCESS_TOKEN')
GITHUB_TOKEN = os.getenv('GITHUB_TOKEN')

# Screenshot Settings
SCREENSHOT_FOLDER = "screenshots"
SCREENSHOT_FORMAT = "PNG"
SCREENSHOT_QUALITY = 95

# Screen Analysis Settings
OCR_LANGUAGE = "eng"  # Tesseract OCR language
VISION_API_TIMEOUT = 30  # seconds
MAX_IMAGE_SIZE = 1920  # pixels

# Web Search Settings
DEFAULT_SEARCH_RESULTS = 5
SEARCH_TIMEOUT = 10  # seconds
PRIORITIZED_DOMAINS = [
    'stackoverflow.com',
    'github.com',
    'docs.python.org',
    'developer.mozilla.org',
    'w3schools.com'
]

# Code Analysis Settings
MAX_FILE_SIZE = 1024 * 1024  # 1MB
SUPPORTED_EXTENSIONS = ['.py', '.js', '.html', '.css', '.json']
CODE_QUALITY_CHECKS = {
    'max_line_length': 120,
    'check_todos': True,
    'check_print_statements': True,
    'check_hardcoded_credentials': True
}

# Note-taking Settings
NOTES_FOLDER = "notes"
NOTES_FORMAT = "markdown"
MAX_NOTES_PER_QUERY = 50

# Reminder Settings
REMINDERS_FILE = "reminders.json"
DEFAULT_REMINDER_SOUND = True
REMINDER_CHECK_INTERVAL = 60  # seconds

# LinkedIn API Settings
LINKEDIN_API_BASE = "https://api.linkedin.com/v2"
LINKEDIN_RATE_LIMIT = 100  # requests per hour

# GitHub API Settings
GITHUB_API_BASE = "https://api.github.com"
GITHUB_RATE_LIMIT = 5000  # requests per hour

# Logging Settings
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOG_FILE = "lia_agent.log"

# Feature Flags
ENABLE_GEMINI_VISION = True
ENABLE_ADVANCED_OCR = True
ENABLE_CODE_EXECUTION = True
ENABLE_WEB_AUTOMATION = True
ENABLE_LINKEDIN_INTEGRATION = False  # Requires API setup
ENABLE_GITHUB_INTEGRATION = True

# Roman Urdu Commands Mapping
COMMAND_MAPPINGS = {
    # Screenshot commands
    'screenshot lo': 'take_screenshot',
    'screen capture karo': 'take_screenshot',
    'tasveer lo': 'take_screenshot',

    # Screen analysis commands
    'screen analyze karo': 'analyze_current_screen',
    'screen dekh kar batao': 'analyze_current_screen',
    'screen mein kya hai': 'analyze_current_screen',

    # Web search commands
    'error solve karo': 'search_code_solutions',
    'code fix dhundo': 'search_code_solutions',
    'tutorial dhundo': 'search_programming_tutorials',
    'tech news dikhao': 'search_tech_news',

    # Code debugging commands
    'code check karo': 'analyze_python_file',
    'file mein errors dhundo': 'analyze_python_file',
    'code run karo': 'run_python_code',

    # General commands
    'website kholo': 'open_website',
    'docs dhundo': 'search_documentation',
    'github repo dhundo': 'search_github_repos'
}

def get_api_key(service: str) -> str:
    """Get API key for a specific service"""
    api_keys = {
        'google_search': GOOGLE_SEARCH_API_KEY,
        'gemini': GEMINI_API_KEY,
        'linkedin': LINKEDIN_ACCESS_TOKEN,
        'github': GITHUB_TOKEN
    }
    return api_keys.get(service.lower(), '')

def is_feature_enabled(feature: str) -> bool:
    """Check if a specific feature is enabled"""
    features = {
        'gemini_vision': ENABLE_GEMINI_VISION,
        'advanced_ocr': ENABLE_ADVANCED_OCR,
        'code_execution': ENABLE_CODE_EXECUTION,
        'web_automation': ENABLE_WEB_AUTOMATION,
        'linkedin': ENABLE_LINKEDIN_INTEGRATION,
        'github': ENABLE_GITHUB_INTEGRATION
    }
    return features.get(feature.lower(), False)

def get_command_function(roman_urdu_command: str) -> str:
    """Map Roman Urdu command to function name"""
    return COMMAND_MAPPINGS.get(roman_urdu_command.lower(), '')

# Validation functions
def validate_api_keys():
    """Validate that required API keys are present"""
    missing_keys = []

    if not GOOGLE_SEARCH_API_KEY:
        missing_keys.append('GOOGLE_SEARCH_API_KEY')
    if not SEARCH_ENGINE_ID:
        missing_keys.append('SEARCH_ENGINE_ID')

    if missing_keys:
        print(f"Warning: Missing API keys: {', '.join(missing_keys)}")
        print("Some features may not work properly.")

    return len(missing_keys) == 0

def create_required_folders():
    """Create required folders if they don't exist"""
    folders = [SCREENSHOT_FOLDER, NOTES_FOLDER]

    for folder in folders:
        if not os.path.exists(folder):
            os.makedirs(folder)
            print(f"Created folder: {folder}")

# Initialize settings on import
if __name__ == "__main__":
    validate_api_keys()
    create_required_folders()
    print("Lia AI Assistant settings loaded successfully!")