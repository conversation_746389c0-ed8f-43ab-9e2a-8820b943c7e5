import os
import ast
import re
import logging
from datetime import datetime
from livekit.agents import llm
import subprocess
import sys
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@llm.ai_callable(
    description="Python file ko analyze kar ke errors aur issues identify karta hai. Roman Urdu command: 'code check karo' ya 'file mein errors dhundo'"
)
def analyze_python_file(file_path: str) -> str:
    """
    Analyze a Python file for syntax errors, potential issues, and code quality

    Args:
        file_path (str): Path to the Python file to analyze

    Returns:
        str: Analysis results with identified issues and suggestions
    """
    try:
        # Check if file exists
        if not os.path.exists(file_path):
            return f"File nahi mila: {file_path}"

        # Read file content
        with open(file_path, 'r', encoding='utf-8') as file:
            code_content = file.read()

        analysis_results = []

        # 1. Syntax Check
        syntax_issues = check_syntax(code_content, file_path)
        if syntax_issues:
            analysis_results.append(f"🔴 Syntax Errors:\n{syntax_issues}")

        # 2. Import Analysis
        import_issues = analyze_imports(code_content)
        if import_issues:
            analysis_results.append(f"⚠️ Import Issues:\n{import_issues}")

        # 3. Code Quality Check
        quality_issues = check_code_quality(code_content)
        if quality_issues:
            analysis_results.append(f"💡 Code Quality Suggestions:\n{quality_issues}")

        # 4. Security Check
        security_issues = check_security_issues(code_content)
        if security_issues:
            analysis_results.append(f"🔒 Security Concerns:\n{security_issues}")

        if not analysis_results:
            return f"✅ File '{file_path}' mein koi major issues nahi mile. Code looks good!"

        return f"Code Analysis Results for '{file_path}':\n\n" + "\n\n".join(analysis_results)

    except Exception as e:
        error_msg = f"File analysis mein error: {str(e)}"
        logger.error(error_msg)
        return error_msg

def check_syntax(code_content: str, file_path: str) -> str:
    """Check for Python syntax errors"""
    try:
        ast.parse(code_content)
        return ""  # No syntax errors
    except SyntaxError as e:
        return f"Line {e.lineno}: {e.msg}\n  {e.text.strip() if e.text else ''}"
    except Exception as e:
        return f"Syntax check mein error: {str(e)}"

def analyze_imports(code_content: str) -> str:
    """Analyze import statements for potential issues"""
    issues = []
    lines = code_content.split('\n')

    # Common problematic patterns
    for i, line in enumerate(lines, 1):
        line = line.strip()

        # Check for wildcard imports
        if re.match(r'from\s+\w+\s+import\s+\*', line):
            issues.append(f"Line {i}: Wildcard import detected - {line}")

        # Check for unused imports (basic check)
        if line.startswith('import ') or line.startswith('from '):
            module_name = extract_module_name(line)
            if module_name and module_name not in code_content[code_content.find(line) + len(line):]:
                issues.append(f"Line {i}: Potentially unused import - {line}")

    return "\n".join(issues) if issues else ""

def extract_module_name(import_line: str) -> str:
    """Extract module name from import statement"""
    try:
        if import_line.startswith('import '):
            return import_line.split('import ')[1].split('.')[0].split(' as ')[0].strip()
        elif import_line.startswith('from '):
            parts = import_line.split()
            if 'import' in parts:
                import_idx = parts.index('import')
                if import_idx + 1 < len(parts):
                    return parts[import_idx + 1].split(',')[0].strip()
    except:
        pass
    return ""

def check_code_quality(code_content: str) -> str:
    """Check for code quality issues"""
    issues = []
    lines = code_content.split('\n')

    for i, line in enumerate(lines, 1):
        original_line = line
        line = line.strip()

        # Check line length
        if len(original_line) > 120:
            issues.append(f"Line {i}: Line too long ({len(original_line)} chars)")

        # Check for TODO/FIXME comments
        if 'TODO' in line.upper() or 'FIXME' in line.upper():
            issues.append(f"Line {i}: TODO/FIXME comment found - {line}")

        # Check for print statements (might be debug code)
        if re.search(r'\bprint\s*\(', line) and not line.startswith('#'):
            issues.append(f"Line {i}: Print statement found (debug code?) - {line}")

        # Check for hardcoded passwords/keys
        if re.search(r'(password|key|secret|token)\s*=\s*["\'][^"\']+["\']', line, re.IGNORECASE):
            issues.append(f"Line {i}: Potential hardcoded credential - {line}")

    return "\n".join(issues) if issues else ""

def check_security_issues(code_content: str) -> str:
    """Check for potential security issues"""
    issues = []
    lines = code_content.split('\n')

    for i, line in enumerate(lines, 1):
        line = line.strip()

        # Check for eval() usage
        if 'eval(' in line:
            issues.append(f"Line {i}: eval() usage detected (security risk)")

        # Check for exec() usage
        if 'exec(' in line:
            issues.append(f"Line {i}: exec() usage detected (security risk)")

        # Check for shell=True in subprocess
        if 'shell=True' in line:
            issues.append(f"Line {i}: subprocess with shell=True (potential security risk)")

        # Check for SQL injection patterns
        if re.search(r'(execute|query).*%.*s', line):
            issues.append(f"Line {i}: Potential SQL injection vulnerability")

    return "\n".join(issues) if issues else ""

@llm.ai_callable(
    description="Python code ko run kar ke errors catch karta hai. Roman Urdu command: 'code run karo' ya 'script execute karo'"
)
def run_python_code(file_path: str) -> str:
    """
    Run Python code and capture any runtime errors

    Args:
        file_path (str): Path to the Python file to run

    Returns:
        str: Execution results or error messages
    """
    try:
        # Check if file exists
        if not os.path.exists(file_path):
            return f"File nahi mila: {file_path}"

        # Run the Python file
        result = subprocess.run(
            [sys.executable, file_path],
            capture_output=True,
            text=True,
            timeout=30  # 30 second timeout
        )

        output_parts = []

        if result.stdout:
            output_parts.append(f"✅ Output:\n{result.stdout}")

        if result.stderr:
            output_parts.append(f"🔴 Errors:\n{result.stderr}")

        if result.returncode != 0:
            output_parts.append(f"❌ Exit code: {result.returncode}")
        else:
            output_parts.append("✅ Code executed successfully!")

        return "\n\n".join(output_parts) if output_parts else "Code run ho gaya lekin koi output nahi aya."

    except subprocess.TimeoutExpired:
        return "⏰ Code execution timeout (30 seconds se zyada time laga)"
    except Exception as e:
        error_msg = f"Code execution mein error: {str(e)}"
        logger.error(error_msg)
        return error_msg

@llm.ai_callable(
    description="Project folder mein saari Python files ko analyze karta hai. Roman Urdu command: 'project check karo' ya 'saari files analyze karo'"
)
def analyze_project_folder(folder_path: str) -> str:
    """
    Analyze all Python files in a project folder

    Args:
        folder_path (str): Path to the project folder

    Returns:
        str: Analysis summary for all Python files
    """
    try:
        if not os.path.exists(folder_path):
            return f"Folder nahi mila: {folder_path}"

        # Find all Python files
        python_files = []
        for root, dirs, files in os.walk(folder_path):
            for file in files:
                if file.endswith('.py'):
                    python_files.append(os.path.join(root, file))

        if not python_files:
            return f"Folder '{folder_path}' mein koi Python files nahi mile."

        analysis_summary = f"Project Analysis for '{folder_path}':\n"
        analysis_summary += f"Found {len(python_files)} Python files\n\n"

        total_issues = 0

        for file_path in python_files[:10]:  # Limit to first 10 files
            relative_path = os.path.relpath(file_path, folder_path)
            file_analysis = analyze_python_file(file_path)

            if "koi major issues nahi mile" in file_analysis:
                analysis_summary += f"✅ {relative_path}: No issues\n"
            else:
                issue_count = file_analysis.count("Line ")
                total_issues += issue_count
                analysis_summary += f"⚠️ {relative_path}: {issue_count} issues found\n"

        if len(python_files) > 10:
            analysis_summary += f"\n... and {len(python_files) - 10} more files\n"

        analysis_summary += f"\nTotal issues found: {total_issues}"

        return analysis_summary

    except Exception as e:
        error_msg = f"Project analysis mein error: {str(e)}"
        logger.error(error_msg)
        return error_msg

@llm.ai_callable(
    description="Code mein specific error message ke liye solution suggest karta hai. Roman Urdu command: 'error fix karo' ya 'solution batao'"
)
def suggest_error_fix(error_message: str, code_snippet: str = "") -> str:
    """
    Suggest fixes for common Python errors

    Args:
        error_message (str): The error message received
        code_snippet (str): Optional code snippet where error occurred

    Returns:
        str: Suggested fixes and explanations
    """
    try:
        suggestions = []
        error_lower = error_message.lower()

        # Common error patterns and their fixes
        if "modulenotfounderror" in error_lower or "no module named" in error_lower:
            module_name = extract_module_from_error(error_message)
            suggestions.append(f"📦 Module '{module_name}' install nahi hai.")
            suggestions.append(f"Fix: pip install {module_name}")
            suggestions.append("Ya phir virtual environment activate karo.")

        elif "syntaxerror" in error_lower:
            suggestions.append("🔴 Syntax Error hai code mein:")
            suggestions.append("- Brackets (), [], {} properly close kiye hain?")
            suggestions.append("- Indentation correct hai?")
            suggestions.append("- Colon (:) missing to nahi?")

        elif "indentationerror" in error_lower:
            suggestions.append("📏 Indentation Error:")
            suggestions.append("- Spaces aur tabs mix na karo")
            suggestions.append("- Consistent indentation use karo (4 spaces recommended)")
            suggestions.append("- Function/class ke andar proper indentation hai?")

        elif "nameerror" in error_lower:
            var_name = extract_variable_from_error(error_message)
            suggestions.append(f"🔍 Variable '{var_name}' defined nahi hai:")
            suggestions.append("- Variable name spelling check karo")
            suggestions.append("- Variable define kiya hai pehle?")
            suggestions.append("- Scope issue to nahi?")

        elif "typeerror" in error_lower:
            suggestions.append("🔄 Type Error:")
            suggestions.append("- Data types compatible hain?")
            suggestions.append("- Function arguments correct hain?")
            suggestions.append("- String/int conversion chahiye?")

        elif "indexerror" in error_lower:
            suggestions.append("📋 Index Error:")
            suggestions.append("- List/array ka size check karo")
            suggestions.append("- Index range mein hai?")
            suggestions.append("- Empty list to nahi?")

        elif "keyerror" in error_lower:
            key_name = extract_key_from_error(error_message)
            suggestions.append(f"🔑 Key '{key_name}' dictionary mein nahi hai:")
            suggestions.append("- Key name spelling check karo")
            suggestions.append("- dict.get() use karo default value ke sath")
            suggestions.append("- 'in' operator se check karo pehle")

        else:
            suggestions.append("🤔 General debugging tips:")
            suggestions.append("- Error message carefully padho")
            suggestions.append("- Line number check karo")
            suggestions.append("- Print statements add kar ke debug karo")
            suggestions.append("- Google mein error search karo")

        if code_snippet:
            suggestions.append(f"\n📝 Code snippet analysis:")
            suggestions.append("Code mein ye issues ho sakte hain:")
            # Basic code analysis
            if "print(" not in code_snippet and "return" not in code_snippet:
                suggestions.append("- Output statements missing hain")

        return "Error Fix Suggestions:\n\n" + "\n".join(suggestions)

    except Exception as e:
        error_msg = f"Error suggestion mein error: {str(e)}"
        logger.error(error_msg)
        return error_msg

def extract_module_from_error(error_message: str) -> str:
    """Extract module name from ModuleNotFoundError"""
    match = re.search(r"No module named '([^']+)'", error_message)
    return match.group(1) if match else "unknown"

def extract_variable_from_error(error_message: str) -> str:
    """Extract variable name from NameError"""
    match = re.search(r"name '([^']+)' is not defined", error_message)
    return match.group(1) if match else "unknown"

def extract_key_from_error(error_message: str) -> str:
    """Extract key name from KeyError"""
    match = re.search(r"KeyError: '([^']+)'", error_message)
    return match.group(1) if match else "unknown"