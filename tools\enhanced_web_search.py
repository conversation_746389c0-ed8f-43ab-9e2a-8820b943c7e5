import os
import requests
import logging
from dotenv import load_dotenv
from livekit.agents import llm
from datetime import datetime
import json
import re
from urllib.parse import quote_plus
import webbrowser

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@llm.ai_callable(
    description="Code debugging ke liye specific programming errors ya solutions search karta hai. Roman Urdu command: 'error solve karo' ya 'code fix dhundo'"
)
def search_code_solutions(error_message: str, programming_language: str = "python") -> str:
    """
    Search for code solutions and debugging help for specific errors

    Args:
        error_message (str): The error message or problem description
        programming_language (str): Programming language (default: python)

    Returns:
        str: Search results with solutions or error message
    """
    try:
        # Construct search query for code solutions
        query = f"{programming_language} {error_message} solution fix stackoverflow"

        # Use Google Custom Search API
        results = perform_google_search(query, num_results=5)

        if not results:
            return f"'{error_message}' ke liye koi solution nahi mila."

        formatted_results = f"Code Solutions for '{error_message}':\n\n"

        for i, result in enumerate(results, 1):
            title = result.get('title', 'No title')
            link = result.get('link', 'No link')
            snippet = result.get('snippet', 'No description')

            # Prioritize StackOverflow, GitHub, and documentation sites
            if any(site in link.lower() for site in ['stackoverflow.com', 'github.com', 'docs.python.org', 'developer.mozilla.org']):
                formatted_results += f"⭐ {i}. {title}\n"
            else:
                formatted_results += f"{i}. {title}\n"

            formatted_results += f"🔗 {link}\n"
            formatted_results += f"📝 {snippet}\n\n"

        return formatted_results.strip()

    except Exception as e:
        error_msg = f"Code solution search mein error: {str(e)}"
        logger.error(error_msg)
        return error_msg

@llm.ai_callable(
    description="Programming tutorials aur learning resources dhundta hai. Roman Urdu command: 'tutorial dhundo' ya 'seekhne ke liye material chahiye'"
)
def search_programming_tutorials(topic: str, skill_level: str = "beginner") -> str:
    """
    Search for programming tutorials and learning resources

    Args:
        topic (str): Programming topic or technology to learn
        skill_level (str): Skill level - beginner, intermediate, advanced

    Returns:
        str: Tutorial and learning resource results
    """
    try:
        # Construct search query for tutorials
        query = f"{topic} tutorial {skill_level} learn programming guide"

        results = perform_google_search(query, num_results=6)

        if not results:
            return f"'{topic}' ke liye tutorials nahi mile."

        formatted_results = f"Programming Tutorials for '{topic}' ({skill_level} level):\n\n"

        for i, result in enumerate(results, 1):
            title = result.get('title', 'No title')
            link = result.get('link', 'No link')
            snippet = result.get('snippet', 'No description')

            # Prioritize educational sites
            if any(site in link.lower() for site in ['youtube.com', 'udemy.com', 'coursera.org', 'freecodecamp.org', 'w3schools.com', 'tutorialspoint.com']):
                formatted_results += f"📚 {i}. {title}\n"
            else:
                formatted_results += f"{i}. {title}\n"

            formatted_results += f"🔗 {link}\n"
            formatted_results += f"📝 {snippet}\n\n"

        return formatted_results.strip()

    except Exception as e:
        error_msg = f"Tutorial search mein error: {str(e)}"
        logger.error(error_msg)
        return error_msg

@llm.ai_callable(
    description="Latest tech news aur updates search karta hai. Roman Urdu command: 'tech news dikhao' ya 'latest updates kya hain'"
)
def search_tech_news(topic: str = "technology") -> str:
    """
    Search for latest technology news and updates

    Args:
        topic (str): Technology topic to search news for

    Returns:
        str: Latest tech news results
    """
    try:
        # Construct search query for recent tech news
        query = f"{topic} news latest 2024 technology updates"

        results = perform_google_search(query, num_results=5)

        if not results:
            return f"'{topic}' ke liye latest news nahi mili."

        formatted_results = f"Latest Tech News for '{topic}':\n\n"

        for i, result in enumerate(results, 1):
            title = result.get('title', 'No title')
            link = result.get('link', 'No link')
            snippet = result.get('snippet', 'No description')

            # Prioritize tech news sites
            if any(site in link.lower() for site in ['techcrunch.com', 'theverge.com', 'wired.com', 'ars-technica.com', 'engadget.com']):
                formatted_results += f"📰 {i}. {title}\n"
            else:
                formatted_results += f"{i}. {title}\n"

            formatted_results += f"🔗 {link}\n"
            formatted_results += f"📝 {snippet}\n\n"

        return formatted_results.strip()

    except Exception as e:
        error_msg = f"Tech news search mein error: {str(e)}"
        logger.error(error_msg)
        return error_msg

def perform_google_search(query: str, num_results: int = 5) -> list:
    """
    Perform Google Custom Search API call

    Args:
        query (str): Search query
        num_results (int): Number of results to return

    Returns:
        list: Search results
    """
    try:
        api_key = os.getenv("GOOGLE_SEARCH_API_KEY")
        search_engine_id = os.getenv("SEARCH_ENGINE_ID")

        if not api_key or not search_engine_id:
            logger.error("Google Search API credentials missing")
            return []

        url = "https://www.googleapis.com/customsearch/v1"
        params = {
            "key": api_key,
            "cx": search_engine_id,
            "q": query,
            "num": min(num_results, 10)  # Google API max is 10
        }

        response = requests.get(url, params=params, timeout=10)

        if response.status_code == 200:
            data = response.json()
            return data.get("items", [])
        else:
            logger.error(f"Google Search API error: {response.status_code}")
            return []

    except Exception as e:
        logger.error(f"Google Search error: {str(e)}")
        return []

@llm.ai_callable(
    description="Browser mein specific URL open karta hai. Roman Urdu command: 'website kholo' ya 'browser mein ye link kholo'"
)
def open_website(url: str) -> str:
    """
    Open a specific website in the default browser

    Args:
        url (str): URL to open

    Returns:
        str: Success or error message
    """
    try:
        # Add https:// if not present
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url

        # Open URL in default browser
        webbrowser.open(url)

        logger.info(f"Opened URL: {url}")
        return f"Website successfully open ho gaya: {url}"

    except Exception as e:
        error_msg = f"Website open karne mein error: {str(e)}"
        logger.error(error_msg)
        return error_msg

@llm.ai_callable(
    description="Quick search kar ke first result browser mein open karta hai. Roman Urdu command: 'search kar ke kholo' ya 'pehla result kholo'"
)
def search_and_open(query: str) -> str:
    """
    Search for a query and open the first result in browser

    Args:
        query (str): Search query

    Returns:
        str: Success message with opened URL or error message
    """
    try:
        # Perform search
        results = perform_google_search(query, num_results=1)

        if not results:
            return f"'{query}' ke liye koi results nahi mile."

        # Get first result URL
        first_result = results[0]
        url = first_result.get('link', '')
        title = first_result.get('title', 'No title')

        if url:
            # Open in browser
            webbrowser.open(url)
            logger.info(f"Opened search result: {url}")
            return f"Search result open ho gaya:\n{title}\n{url}"
        else:
            return "Valid URL nahi mila search results mein."

    except Exception as e:
        error_msg = f"Search and open mein error: {str(e)}"
        logger.error(error_msg)
        return error_msg

@llm.ai_callable(
    description="Documentation ya API reference search kar ke open karta hai. Roman Urdu command: 'docs dhundo' ya 'documentation kholo'"
)
def search_documentation(technology: str, topic: str = "") -> str:
    """
    Search for official documentation and open it

    Args:
        technology (str): Technology/library name (e.g., 'python', 'react', 'django')
        topic (str): Specific topic within the documentation

    Returns:
        str: Documentation search results
    """
    try:
        # Construct search query for official documentation
        if topic:
            query = f"{technology} {topic} official documentation site:docs"
        else:
            query = f"{technology} official documentation site:docs"

        results = perform_google_search(query, num_results=3)

        if not results:
            return f"'{technology}' ke liye documentation nahi mila."

        formatted_results = f"Documentation for '{technology}':\n\n"

        for i, result in enumerate(results, 1):
            title = result.get('title', 'No title')
            link = result.get('link', 'No link')
            snippet = result.get('snippet', 'No description')

            # Prioritize official documentation sites
            if any(site in link.lower() for site in ['docs.', 'documentation', 'api.', 'developer.']):
                formatted_results += f"📖 {i}. {title}\n"
            else:
                formatted_results += f"{i}. {title}\n"

            formatted_results += f"🔗 {link}\n"
            formatted_results += f"📝 {snippet}\n\n"

        # Auto-open first result if it looks like official docs
        first_result = results[0]
        first_url = first_result.get('link', '')
        if any(site in first_url.lower() for site in ['docs.', 'documentation', 'api.']):
            webbrowser.open(first_url)
            formatted_results += f"✅ First documentation link automatically opened in browser."

        return formatted_results.strip()

    except Exception as e:
        error_msg = f"Documentation search mein error: {str(e)}"
        logger.error(error_msg)
        return error_msg

@llm.ai_callable(
    description="GitHub repositories search karta hai specific topic ke liye. Roman Urdu command: 'github repo dhundo' ya 'code examples chahiye'"
)
def search_github_repos(topic: str, language: str = "") -> str:
    """
    Search for GitHub repositories related to a topic

    Args:
        topic (str): Topic or technology to search for
        language (str): Programming language filter (optional)

    Returns:
        str: GitHub repository search results
    """
    try:
        # Construct search query for GitHub repositories
        if language:
            query = f"{topic} {language} site:github.com repository"
        else:
            query = f"{topic} site:github.com repository"

        results = perform_google_search(query, num_results=5)

        if not results:
            return f"'{topic}' ke liye GitHub repositories nahi mile."

        formatted_results = f"GitHub Repositories for '{topic}':\n\n"

        for i, result in enumerate(results, 1):
            title = result.get('title', 'No title')
            link = result.get('link', 'No link')
            snippet = result.get('snippet', 'No description')

            # Only include GitHub links
            if 'github.com' in link.lower():
                formatted_results += f"⭐ {i}. {title}\n"
                formatted_results += f"🔗 {link}\n"
                formatted_results += f"📝 {snippet}\n\n"

        return formatted_results.strip()

    except Exception as e:
        error_msg = f"GitHub search mein error: {str(e)}"
        logger.error(error_msg)
        return error_msg