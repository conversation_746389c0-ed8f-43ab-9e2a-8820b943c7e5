import os
import base64
import pyautogui
import cv2
import numpy as np
from PIL import Image
import pytesseract
from datetime import datetime
from livekit.agents import llm
import logging
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def capture_screen_for_analysis():
    """Capture current screen for analysis"""
    try:
        screenshot = pyautogui.screenshot()
        # Convert PIL image to numpy array for OpenCV
        img_array = np.array(screenshot)
        # Convert RGB to BGR for OpenCV
        img_bgr = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
        return screenshot, img_bgr
    except Exception as e:
        logger.error(f"Error capturing screen: {str(e)}")
        return None, None

def encode_image_to_base64(image):
    """Convert PIL image to base64 string for API calls"""
    try:
        import io
        buffer = io.BytesIO()
        image.save(buffer, format='PNG')
        img_str = base64.b64encode(buffer.getvalue()).decode()
        return img_str
    except Exception as e:
        logger.error(f"Error encoding image: {str(e)}")
        return None

@llm.ai_callable(
    description="Screen ko analyze karta hai aur batata hai kya dikh raha hai. Roman Urdu command: 'screen analyze karo' ya 'screen dekh kar batao'"
)
def analyze_current_screen() -> str:
    """
    Analyze the current screen and describe what's visible

    Returns:
        str: Description of what's on the screen or error message
    """
    try:
        # Capture screen
        pil_image, cv_image = capture_screen_for_analysis()
        if pil_image is None:
            return "Screen capture karne mein error aya."

        # Try OCR first for text extraction
        text_content = extract_text_from_screen(pil_image)

        # Basic image analysis using OpenCV
        analysis_result = perform_basic_image_analysis(cv_image)

        result = "Screen Analysis:\n"
        if text_content.strip():
            result += f"Text Content: {text_content[:200]}...\n"

        result += f"Visual Analysis: {analysis_result}"

        return result

    except Exception as e:
        error_msg = f"Screen analysis mein error: {str(e)}"
        logger.error(error_msg)
        return error_msg

def extract_text_from_screen(image):
    """Extract text from screen using OCR"""
    try:
        # Use pytesseract for OCR
        text = pytesseract.image_to_string(image)
        return text.strip()
    except Exception as e:
        logger.error(f"OCR error: {str(e)}")
        return "OCR text extraction mein error."

def perform_basic_image_analysis(cv_image):
    """Perform basic image analysis using OpenCV"""
    try:
        # Convert to grayscale
        gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)

        # Get image dimensions
        height, width = gray.shape

        # Calculate brightness
        brightness = np.mean(gray)

        # Detect edges
        edges = cv2.Canny(gray, 50, 150)
        edge_count = np.count_nonzero(edges)

        # Detect contours (objects)
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        object_count = len(contours)

        analysis = f"Screen size: {width}x{height}, "
        analysis += f"Brightness: {brightness:.1f}/255, "
        analysis += f"Objects detected: {object_count}, "

        if brightness < 50:
            analysis += "Screen bahut dark hai. "
        elif brightness > 200:
            analysis += "Screen bahut bright hai. "
        else:
            analysis += "Screen brightness normal hai. "

        if object_count > 50:
            analysis += "Screen mein bahut saare elements hain."
        elif object_count > 20:
            analysis += "Screen mein moderate elements hain."
        else:
            analysis += "Screen mein kam elements hain."

        return analysis

    except Exception as e:
        logger.error(f"Image analysis error: {str(e)}")
        return "Basic image analysis mein error."

@llm.ai_callable(
    description="Screen mein specific object ya text dhundta hai. Roman Urdu command: 'screen mein [object] dhundo' ya '[text] screen mein hai kya'"
)
def find_on_screen(search_term: str) -> str:
    """
    Find specific object or text on the current screen

    Args:
        search_term (str): What to search for on the screen

    Returns:
        str: Search results or error message
    """
    try:
        # Capture screen
        pil_image, cv_image = capture_screen_for_analysis()
        if pil_image is None:
            return "Screen capture karne mein error aya."

        # Extract all text from screen
        full_text = extract_text_from_screen(pil_image)

        # Search for the term (case insensitive)
        search_lower = search_term.lower()
        text_lower = full_text.lower()

        if search_lower in text_lower:
            # Find the context around the found text
            index = text_lower.find(search_lower)
            start = max(0, index - 50)
            end = min(len(full_text), index + len(search_term) + 50)
            context = full_text[start:end]

            return f"'{search_term}' mil gaya screen mein! Context: ...{context}..."
        else:
            return f"'{search_term}' screen mein nahi mila."

    except Exception as e:
        error_msg = f"Screen search mein error: {str(e)}"
        logger.error(error_msg)
        return error_msg

@llm.ai_callable(
    description="Google Gemini Vision API use kar ke screen ko detail mein analyze karta hai. Roman Urdu command: 'screen ko detail mein dekho' ya 'advanced analysis karo'"
)
def analyze_screen_with_gemini() -> str:
    """
    Analyze screen using Google Gemini Vision API for detailed analysis

    Returns:
        str: Detailed analysis from Gemini Vision or error message
    """
    try:
        # Check if Gemini API key is available
        gemini_api_key = os.getenv('GEMINI_API_KEY')
        if not gemini_api_key:
            return "Gemini API key nahi mila. .env file mein GEMINI_API_KEY add karo."

        # Capture screen
        pil_image, _ = capture_screen_for_analysis()
        if pil_image is None:
            return "Screen capture karne mein error aya."

        # Encode image to base64
        img_base64 = encode_image_to_base64(pil_image)
        if not img_base64:
            return "Image encoding mein error."

        # Call Gemini Vision API
        response = call_gemini_vision_api(img_base64, gemini_api_key)

        if response:
            return f"Gemini Vision Analysis:\n{response}"
        else:
            return "Gemini API se response nahi aya."

    except Exception as e:
        error_msg = f"Gemini vision analysis mein error: {str(e)}"
        logger.error(error_msg)
        return error_msg

def call_gemini_vision_api(image_base64, api_key):
    """Call Google Gemini Vision API"""
    try:
        url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-pro-vision:generateContent?key={api_key}"

        headers = {
            'Content-Type': 'application/json',
        }

        payload = {
            "contents": [{
                "parts": [
                    {"text": "Analyze this screen image and describe what you see in detail. Include any text, UI elements, applications, and overall context."},
                    {
                        "inline_data": {
                            "mime_type": "image/png",
                            "data": image_base64
                        }
                    }
                ]
            }]
        }

        response = requests.post(url, headers=headers, json=payload, timeout=30)

        if response.status_code == 200:
            result = response.json()
            if 'candidates' in result and len(result['candidates']) > 0:
                return result['candidates'][0]['content']['parts'][0]['text']
            else:
                return "Gemini se proper response nahi aya."
        else:
            logger.error(f"Gemini API error: {response.status_code} - {response.text}")
            return f"Gemini API error: {response.status_code}"

    except Exception as e:
        logger.error(f"Gemini API call error: {str(e)}")
        return f"Gemini API call mein error: {str(e)}"

@llm.ai_callable(
    description="Object detection kar ke screen mein objects identify karta hai. Roman Urdu command: 'objects detect karo' ya 'screen mein kya objects hain'"
)
def detect_objects_on_screen() -> str:
    """
    Detect and identify objects on the current screen using computer vision

    Returns:
        str: List of detected objects or error message
    """
    try:
        # Capture screen
        pil_image, cv_image = capture_screen_for_analysis()
        if pil_image is None:
            return "Screen capture karne mein error aya."

        # Basic object detection using contours
        objects = detect_basic_objects(cv_image)

        # Color analysis
        colors = analyze_dominant_colors(cv_image)

        result = "Object Detection Results:\n"
        result += f"Detected shapes/objects: {objects}\n"
        result += f"Dominant colors: {colors}"

        return result

    except Exception as e:
        error_msg = f"Object detection mein error: {str(e)}"
        logger.error(error_msg)
        return error_msg

def detect_basic_objects(cv_image):
    """Detect basic shapes and objects using OpenCV"""
    try:
        gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)

        # Apply Gaussian blur
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)

        # Edge detection
        edges = cv2.Canny(blurred, 50, 150)

        # Find contours
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        shapes = {
            'rectangles': 0,
            'circles': 0,
            'triangles': 0,
            'other_shapes': 0
        }

        for contour in contours:
            # Filter small contours
            if cv2.contourArea(contour) < 100:
                continue

            # Approximate contour
            epsilon = 0.02 * cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, epsilon, True)

            # Classify shape based on number of vertices
            vertices = len(approx)

            if vertices == 3:
                shapes['triangles'] += 1
            elif vertices == 4:
                shapes['rectangles'] += 1
            elif vertices > 8:
                shapes['circles'] += 1
            else:
                shapes['other_shapes'] += 1

        return f"Rectangles: {shapes['rectangles']}, Circles: {shapes['circles']}, Triangles: {shapes['triangles']}, Other: {shapes['other_shapes']}"

    except Exception as e:
        logger.error(f"Shape detection error: {str(e)}")
        return "Shape detection mein error."

def analyze_dominant_colors(cv_image):
    """Analyze dominant colors in the image"""
    try:
        # Convert BGR to RGB
        rgb_image = cv2.cvtColor(cv_image, cv2.COLOR_BGR2RGB)

        # Reshape image to be a list of pixels
        pixels = rgb_image.reshape((-1, 3))

        # Use k-means clustering to find dominant colors
        from sklearn.cluster import KMeans

        # Reduce number of pixels for faster processing
        pixels_sample = pixels[::100]  # Take every 100th pixel

        kmeans = KMeans(n_clusters=3, random_state=42, n_init=10)
        kmeans.fit(pixels_sample)

        colors = kmeans.cluster_centers_.astype(int)

        color_names = []
        for color in colors:
            r, g, b = color
            color_name = get_color_name(r, g, b)
            color_names.append(f"{color_name} ({r},{g},{b})")

        return ", ".join(color_names)

    except Exception as e:
        logger.error(f"Color analysis error: {str(e)}")
        return "Color analysis mein error."

def get_color_name(r, g, b):
    """Get approximate color name from RGB values"""
    if r > 200 and g > 200 and b > 200:
        return "White/Light"
    elif r < 50 and g < 50 and b < 50:
        return "Black/Dark"
    elif r > g and r > b:
        return "Red-ish"
    elif g > r and g > b:
        return "Green-ish"
    elif b > r and b > g:
        return "Blue-ish"
    elif r > 150 and g > 150:
        return "Yellow-ish"
    elif r > 150 and b > 150:
        return "Magenta-ish"
    elif g > 150 and b > 150:
        return "Cyan-ish"
    else:
        return "Mixed"