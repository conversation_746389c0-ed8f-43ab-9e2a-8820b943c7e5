import os
import pyautogui
from datetime import datetime
from livekit.agents import llm
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_screenshots_folder():
    """Create screenshots folder if it doesn't exist"""
    screenshots_dir = "screenshots"
    if not os.path.exists(screenshots_dir):
        os.makedirs(screenshots_dir)
        logger.info(f"Created screenshots directory: {screenshots_dir}")
    return screenshots_dir

def generate_timestamp_filename():
    """Generate filename with current timestamp"""
    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    return f"screenshot_{timestamp}.png"

@llm.ai_callable(
    description="Screen ka screenshot le kar screenshots folder mein save karta hai timestamp ke sath. Roman Urdu mein command: 'screenshot lo' ya 'screen capture karo'"
)
def take_screenshot() -> str:
    """
    Take a screenshot of the current screen and save it to screenshots folder
    with timestamp filename format: screenshot_YYYY-MM-DD_HH-MM-SS.png

    Returns:
        str: Success message with file path or error message
    """
    try:
        # Create screenshots folder if it doesn't exist
        screenshots_dir = create_screenshots_folder()

        # Generate timestamp filename
        filename = generate_timestamp_filename()
        filepath = os.path.join(screenshots_dir, filename)

        # Take screenshot
        screenshot = pyautogui.screenshot()

        # Save screenshot
        screenshot.save(filepath)

        logger.info(f"Screenshot saved successfully: {filepath}")
        return f"Screenshot successfully save ho gaya: {filepath}"

    except Exception as e:
        error_msg = f"Screenshot lene mein error: {str(e)}"
        logger.error(error_msg)
        return error_msg

@llm.ai_callable(
    description="Specific region ka screenshot leta hai coordinates ke sath. Roman Urdu command: 'region screenshot lo' ya 'area capture karo'"
)
def take_region_screenshot(x: int, y: int, width: int, height: int) -> str:
    """
    Take a screenshot of a specific region of the screen

    Args:
        x (int): X coordinate of top-left corner
        y (int): Y coordinate of top-left corner
        width (int): Width of the region
        height (int): Height of the region

    Returns:
        str: Success message with file path or error message
    """
    try:
        # Create screenshots folder if it doesn't exist
        screenshots_dir = create_screenshots_folder()

        # Generate timestamp filename with region info
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        filename = f"screenshot_region_{timestamp}_{x}_{y}_{width}_{height}.png"
        filepath = os.path.join(screenshots_dir, filename)

        # Take region screenshot
        screenshot = pyautogui.screenshot(region=(x, y, width, height))

        # Save screenshot
        screenshot.save(filepath)

        logger.info(f"Region screenshot saved successfully: {filepath}")
        return f"Region screenshot successfully save ho gaya: {filepath}"

    except Exception as e:
        error_msg = f"Region screenshot lene mein error: {str(e)}"
        logger.error(error_msg)
        return error_msg

@llm.ai_callable(
    description="Screenshots folder mein saved screenshots ki list dikhata hai. Roman Urdu command: 'screenshots dikhao' ya 'saved images list karo'"
)
def list_screenshots() -> str:
    """
    List all screenshots in the screenshots folder

    Returns:
        str: List of screenshot files or message if none found
    """
    try:
        screenshots_dir = "screenshots"

        if not os.path.exists(screenshots_dir):
            return "Screenshots folder abhi tak nahi bana hai. Pehle koi screenshot lo."

        # Get all PNG files in screenshots directory
        screenshot_files = [f for f in os.listdir(screenshots_dir) if f.endswith('.png')]

        if not screenshot_files:
            return "Screenshots folder mein koi file nahi hai."

        # Sort files by modification time (newest first)
        screenshot_files.sort(key=lambda x: os.path.getmtime(os.path.join(screenshots_dir, x)), reverse=True)

        result = f"Screenshots folder mein {len(screenshot_files)} files hain:\n"
        for i, file in enumerate(screenshot_files[:10], 1):  # Show only latest 10
            file_path = os.path.join(screenshots_dir, file)
            file_size = os.path.getsize(file_path) / 1024  # Size in KB
            result += f"{i}. {file} ({file_size:.1f} KB)\n"

        if len(screenshot_files) > 10:
            result += f"... aur {len(screenshot_files) - 10} files"

        return result

    except Exception as e:
        error_msg = f"Screenshots list karne mein error: {str(e)}"
        logger.error(error_msg)
        return error_msg