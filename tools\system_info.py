import psutil
import platform
import logging
from livekit.agents import function_tool

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@function_tool
async def get_system_info() -> str:
    try:
        logger.info("System ki information hasil kar rahi hoon...")
        
        # System info
        system = platform.system()
        release = platform.release()
        machine = platform.machine()
        
        # CPU info
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        
        # Memory info
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        memory_total = memory.total // (1024 * 1024 * 1024)  # Convert to GB
        
        # Disk info
        disk = psutil.disk_usage('/')
        disk_percent = disk.percent
        disk_total = disk.total // (1024 * 1024 * 1024)  # Convert to GB
        
        info = f"""System Information:
- OS: {system} {release} ({machine})
- CPU Usage: {cpu_percent}% (Total Cores: {cpu_count})
- Memory Usage: {memory_percent}% (Total: {memory_total}GB)
- Disk Usage: {disk_percent}% (Total: {disk_total}GB)"""
        
        logger.info("System ki information mil gayi")
        return info
        
    except Exception as e:
        logger.error(f"System information hasil karne mein error aaya: {e}")
        return f"System information hasil nahi ho saki: {e}"

@function_tool
async def get_battery_info() -> str:
    try:
        logger.info("Battery ki information hasil kar rahi hoon...")
        battery = psutil.sensors_battery()
        
        if battery:
            percent = battery.percent
            power_plugged = "Charging" if battery.power_plugged else "Not Charging"
            
            info = f"""Battery Information:
- Battery Level: {percent}%
- Power Status: {power_plugged}"""
            
            logger.info("Battery ki information mil gayi")
            return info
        else:
            return "Battery information dastiyaab nahi hai (possibly a desktop system)"
            
    except Exception as e:
        logger.error(f"Battery information hasil karne mein error aaya: {e}")
        return f"Battery information hasil nahi ho saki: {e}"